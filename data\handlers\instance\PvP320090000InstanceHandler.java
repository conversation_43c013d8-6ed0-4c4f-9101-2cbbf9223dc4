package instance;

import java.util.ArrayList;
import java.util.List;

import com.aionemu.gameserver.instance.handlers.GeneralInstanceHandler;
import com.aionemu.gameserver.instance.handlers.InstanceID;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.services.FFAService;
import com.aionemu.gameserver.services.OneVsOneService;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Unified PvP Instance Handler for Custom 1v1 Map (320090000)
 * 
 * Handles both OneVsOne and FFA events in the same instance map.
 * Determines which system to use based on player participation.
 * 
 * <AUTHOR> System
 */
@InstanceID(320090000)
public class PvP320090000InstanceHandler extends GeneralInstanceHandler {

    // FFA spawn locations for this map (if FFA is used on this map)
    private final List<float[]> ffaSpawnLocations = new ArrayList<>();

    public PvP320090000InstanceHandler(WorldMapInstance instance) {
        super(instance);
        initializeFFASpawnLocations();
    }

    private void initializeFFASpawnLocations() {
        // Add FFA spawn locations for this map (using OneVsOne coordinates as base)
        ffaSpawnLocations.add(new float[]{231.461f, 239.409f, 158.881f, 0});
        ffaSpawnLocations.add(new float[]{317.16235f, 239.97896f, 158.89537f, 58});
        // Add more spawn points around the map for FFA if needed
        ffaSpawnLocations.add(new float[]{274.0f, 220.0f, 158.9f, 30});
        ffaSpawnLocations.add(new float[]{274.0f, 260.0f, 158.9f, 90});
        ffaSpawnLocations.add(new float[]{250.0f, 240.0f, 158.9f, 45});
        ffaSpawnLocations.add(new float[]{300.0f, 240.0f, 158.9f, 135});
    }

    public List<float[]> getFFASpawnLocations() {
        return ffaSpawnLocations;
    }

    @Override
    public void onEnterInstance(Player player) {
        super.onEnterInstance(player);
        // Player entered PvP instance
        System.out.println("DEBUG: Player " + player.getName() + " entered PvP instance 320090000");
    }

    @Override
    public void onLeaveInstance(Player player) {
        super.onLeaveInstance(player);
        
        System.out.println("DEBUG: Player " + player.getName() + " leaving PvP instance 320090000");
        
        // Clean up player from OneVsOne system when they leave
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            System.out.println("DEBUG: Cleaning up OneVsOne match for leaving player " + player.getName());
            OneVsOneService.getInstance().handlePlayerLeave(player);
        }
        
        // FFA cleanup is handled by FFAService when players leave the event
    }

    @Override
    public boolean onDie(Player player, Creature lastAttacker) {
        System.out.println("DEBUG: PvP320090000 onDie called for player: " + player.getName());
        System.out.println("DEBUG: lastAttacker: " + (lastAttacker != null ? lastAttacker.getName() : "null"));
        System.out.println("DEBUG: lastAttacker instanceof Player: " + (lastAttacker instanceof Player));

        // Check FFA first (higher priority for multi-player events)
        boolean isFFAParticipant = FFAService.getInstance().isParticipant(player);
        System.out.println("DEBUG: isFFAParticipant for " + player.getName() + ": " + isFFAParticipant);

        if (isFFAParticipant) {
            System.out.println("FFA Death: " + player.getName() + " killed by " +
                (lastAttacker instanceof Player ? ((Player) lastAttacker).getName() : "unknown"));

            // Handle kill tracking first
            if (lastAttacker instanceof Player) {
                System.out.println("DEBUG: Calling FFAService.onPlayerKill for killer: " + ((Player) lastAttacker).getName() + ", victim: " + player.getName());
                FFAService.getInstance().onPlayerKill((Player) lastAttacker, player);
            } else {
                System.out.println("DEBUG: Not calling onPlayerKill - lastAttacker is not a Player");
            }

            // FFA handles its own resurrection logic
            return true; // Prevent default death handling and resurrection dialog
        }
        
        // Check OneVsOne second
        if (OneVsOneService.getInstance().isPlayerInActiveMatch(player)) {
            System.out.println("DEBUG: OneVsOne death detected for " + player.getName());
            
            // Always immediately resurrect players in OneVsOne matches to prevent resurrection dialog
            PlayerReviveService.revive(player, 100, 100, false, 0);
            player.getGameStats().updateStatsAndSpeedVisually();
            player.unsetResPosState();
            player.setPlayerResActivate(false);
            
            System.out.println("DEBUG: Player " + player.getName() + " resurrected after OneVsOne death");

            // Handle player death in OneVsOne match after resurrection
            if (lastAttacker instanceof Player) {
                System.out.println("DEBUG: Calling OneVsOneService.onPlayerKill for killer: " + ((Player) lastAttacker).getName() + ", victim: " + player.getName());
                OneVsOneService.getInstance().onPlayerKill((Player) lastAttacker, player);
            } else {
                System.out.println("DEBUG: OneVsOne death but lastAttacker is not a Player");
            }
            return true; // Prevent default death handling and resurrection dialog
        }

        System.out.println("DEBUG: Player " + player.getName() + " died but not in any PvP event - using default death handling");
        // Default death handling for non-PvP situations
        return super.onDie(player, lastAttacker);
    }

    @Override
    public void onInstanceDestroy() {
        System.out.println("DEBUG: PvP instance 320090000 being destroyed");
        // Clean up any remaining tasks or resources
        super.onInstanceDestroy();
    }
}
