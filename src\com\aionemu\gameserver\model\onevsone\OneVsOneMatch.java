package com.aionemu.gameserver.model.onevsone;

import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Represents a OneVsOne match with round-based combat system
 * 
 * <AUTHOR> System
 */
public class OneVsOneMatch {
    
    private final OneVsOneParticipant player1;
    private final OneVsOneParticipant player2;
    private final int mapId;
    private final int instanceId;
    private final WorldMapInstance instance;
    private final long matchStartTime;
    
    // Round tracking
    private int player1Score = 0;
    private int player2Score = 0;
    private int currentRound = 1;
    private final int maxRounds = 2; // Best of 3 (first to 2 wins)
    
    // Match state
    private MatchState state = MatchState.PREPARING;
    private boolean isCountdownActive = false;
    private long roundStartTime = 0;
    
    // Spawn coordinates for both players
    private float[] player1SpawnCoords;
    private float[] player2SpawnCoords;
    
    public enum MatchState {
        PREPARING,      // Match created, players being teleported
        COUNTDOWN,      // 20-second countdown before round starts
        FIGHTING,       // Round in progress
        ROUND_END,      // Round ended, preparing for next round
        MATCH_END       // Match completed
    }
    
    public OneVsOneMatch(OneVsOneParticipant player1, OneVsOneParticipant player2, 
                        int mapId, int instanceId, WorldMapInstance instance,
                        float[] player1Coords, float[] player2Coords) {
        this.player1 = player1;
        this.player2 = player2;
        this.mapId = mapId;
        this.instanceId = instanceId;
        this.instance = instance;
        this.matchStartTime = System.currentTimeMillis();
        this.player1SpawnCoords = player1Coords;
        this.player2SpawnCoords = player2Coords;
    }
    
    // Getters
    public OneVsOneParticipant getPlayer1() { return player1; }
    public OneVsOneParticipant getPlayer2() { return player2; }
    public int getMapId() { return mapId; }
    public int getInstanceId() { return instanceId; }
    public WorldMapInstance getInstance() { return instance; }
    public long getMatchStartTime() { return matchStartTime; }
    
    public int getPlayer1Score() { return player1Score; }
    public int getPlayer2Score() { return player2Score; }
    public int getCurrentRound() { return currentRound; }
    public int getMaxRounds() { return maxRounds; }
    
    public MatchState getState() { return state; }
    public void setState(MatchState state) { this.state = state; }
    
    public boolean isCountdownActive() { return isCountdownActive; }
    public void setCountdownActive(boolean active) { this.isCountdownActive = active; }
    
    public long getRoundStartTime() { return roundStartTime; }
    public void setRoundStartTime(long time) { this.roundStartTime = time; }
    
    public float[] getPlayer1SpawnCoords() { return player1SpawnCoords; }
    public float[] getPlayer2SpawnCoords() { return player2SpawnCoords; }
    
    // Score management
    public void addScoreToPlayer1() {
        player1Score++;
        if (player1Score >= maxRounds) {
            state = MatchState.MATCH_END;
        } else {
            currentRound++;
            state = MatchState.ROUND_END;
        }
    }
    
    public void addScoreToPlayer2() {
        player2Score++;
        if (player2Score >= maxRounds) {
            state = MatchState.MATCH_END;
        } else {
            currentRound++;
            state = MatchState.ROUND_END;
        }
    }
    
    // Check if match is complete
    public boolean isMatchComplete() {
        return player1Score >= maxRounds || player2Score >= maxRounds;
    }
    
    // Get winner
    public OneVsOneParticipant getWinner() {
        if (player1Score >= maxRounds) return player1;
        if (player2Score >= maxRounds) return player2;
        return null;
    }
    
    // Get loser
    public OneVsOneParticipant getLoser() {
        if (player1Score >= maxRounds) return player2;
        if (player2Score >= maxRounds) return player1;
        return null;
    }
    
    // Check if player is in this match
    public boolean hasPlayer(int playerId) {
        return player1.getPlayerId() == playerId || player2.getPlayerId() == playerId;
    }
    
    // Get opponent of a player
    public OneVsOneParticipant getOpponent(int playerId) {
        if (player1.getPlayerId() == playerId) return player2;
        if (player2.getPlayerId() == playerId) return player1;
        return null;
    }
    
    // Get participant by player ID
    public OneVsOneParticipant getParticipant(int playerId) {
        if (player1.getPlayerId() == playerId) return player1;
        if (player2.getPlayerId() == playerId) return player2;
        return null;
    }
    
    // Get score string for announcements
    public String getScoreString() {
        return player1Score + "-" + player2Score;
    }
    
    // Get round announcement string
    public String getRoundAnnouncement() {
        if (currentRound == 1) return "ROUND 1";
        if (currentRound == 2) return "ROUND 2";
        if (currentRound == 3) return "FINAL ROUND";
        return "ROUND " + currentRound;
    }
}
