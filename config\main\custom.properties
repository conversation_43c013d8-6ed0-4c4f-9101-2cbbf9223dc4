#
# ----------------------------
# Custom config's:
# ----------------------------

# Enable challenge tasks
# Default: true
gameserver.challenge.tasks.enabled = true

# Announce when a player successfully enchants an item to +15 or +20
# Default: true
gameserver.enchant.announce.enable = true

# Enable speaking between factions
# Default: false
gameserver.chat.factions.enable = false

# Minimum level to use whisper
# Default: 10
gameserver.chat.whisper.level = 10

# Time in days after which an item in broker will be unregistered (client cannot display more than 255 days)
# Default: 8
gameserver.broker.registration_expiration_days = 8

# Allow opposite factions to bind in enemy territories
# Default: false
gameserver.cross.faction.binding = false

# Enable second class change without quest
# Default: false
gameserver.simple.secondclass.enable = false

# Disable chain trigger rate (chain skill with 100% success)
# Default: false
gameserver.skill.chain.disable_triggerrate = false

# Enable ride restriction
# Default: true
gameserver.ride.restriction.enable = true

# Default player fly time
# Default: 60 (1 minute)
gameserver.base.flytime = 60

# Enable no AP in mentored group.
# Default: false
gameserver.noap.mentor.group = false

# Enable one kisk restriction
# Default: true
gameserver.kisk.restriction.enable = true

# ----------------------------
# PvP config's:
# ----------------------------
# Time in milliseconds in which players are limited for killing one player
# Default: 86400000 (1 day)
gameserver.pvp.dayduration = 86400000

# Maximum number of pvp kills on one player before receiving 1AP per kill in a 24 hour period.
# Default: 5
gameserver.pvp.maxkills = 25

# Enable the rewards for pvp kills
# Default: false
# (Reset all_kill from abyss_rank table before activate it)
gameserver.kill.reward.enable = false

# ----------------------------
# Devil's Mark config:
# ----------------------------
# Enable the Devil's Mark PvP bounty system
# Every 3 hours, one random player gets marked and hunted by others
# Default: true
gameserver.devils.mark.enable = true

# Minimum level required to be eligible for Devil's Mark
# Default: 30
gameserver.devils.mark.min.level = 60

# Duration of Devil's Mark in hours
# Default: 3
gameserver.devils.mark.duration = 1

# AP reward for killing marked player
# Default: 5000
gameserver.devils.mark.killer.ap = 5000

# GP reward for killing marked player
# Default: 1000
gameserver.devils.mark.killer.gp = 50

# Item rewards for killing marked player (comma-separated item IDs)
# Default: 186000242 (Ceramium Medal)
# Example: 186000242,186000236,188053083 (multiple items)
gameserver.devils.mark.killer.items = 186000242,186000238,162000124

# Item counts for killing marked player (comma-separated, matches items)
# Default: 5
# Example: 5,3,1 (matches the item order above)
gameserver.devils.mark.killer.counts = 5,100,50

# AP reward for marked player killing normal players
# Default: 3000
gameserver.devils.mark.marked.killer.ap = 300

# GP reward for marked player killing normal players
# Default: 500
gameserver.devils.mark.marked.killer.gp = 80

# Item rewards for marked player killing normal players (comma-separated item IDs)
# Default: 186000242 (Ceramium Medal)
# Example: 186000242,186000236 (multiple items)
gameserver.devils.mark.marked.killer.items = 186000242,186000238,162000124

# Item counts for marked player killing normal players (comma-separated, matches items)
# Default: 3
# Example: 3,1 (matches the item order above)
gameserver.devils.mark.marked.killer.counts = 2,100,50

# AP reward for surviving Devil's Mark
# Default: 2000
gameserver.devils.mark.survival.ap = 20000

# Item rewards for surviving Devil's Mark (comma-separated item IDs)
# Default: 186000242 (Ceramium Medal)
# Example: 186000242,186000236 (multiple items)
gameserver.devils.mark.survival.items = 186000242,186000238,162000124

# Item counts for surviving Devil's Mark (comma-separated, matches items)
# Default: 2
# Example: 2,1 (matches the item order above)
gameserver.devils.mark.survival.counts = 5,150,50

# Visual effect skill IDs for Devil's Mark (comma-separated)
# Default: 1540 (Cool looking buff effect - lasts full duration)
# Example: 1540,3775,3581 (multiple effects stacked)
# Other options: 3775 (Fear effect), 3581 (Withering Gloom), 1417 (Curse Tree)
gameserver.devils.mark.visual.effects = 1540,2579

# Healing skill IDs for Devil's Mark (comma-separated)
# These skills will be applied every X seconds to help the marked player survive
# Default: 3980 (Healing skill)
# Example: 3980,1234 (multiple healing skills)
gameserver.devils.mark.healing.skills = 3980

# Healing interval in seconds for Devil's Mark
# How often healing skills are applied to the marked player
# Default: 30 (every 30 seconds)
gameserver.devils.mark.healing.interval = 30

# Enable healing for Devil's Mark
# Set to false to disable periodic healing for marked players
# Default: true
gameserver.devils.mark.healing.enable = true

# Make marked players vulnerable everywhere (removes safe zone protection)
# Default: true
gameserver.devils.mark.remove.safe.zone.protection = true

# Allow cross-faction targeting (Elyos can attack Asmodian marked players and vice versa)
# Default: true
gameserver.devils.mark.cross.faction.targeting = true

# ----------------------------
# MixFight Event config:
# ----------------------------
# Enable the MixFight custom PvP event system
# Scheduled PvP events where all participants can attack each other
# Default: true
gameserver.mixfight.enable = true

# Minimum level required to participate in MixFight events
# Default: 50
gameserver.mixfight.min.level = 50

# Duration of MixFight event in minutes
# Default: 60
gameserver.mixfight.duration = 60

# Portal availability duration before event starts (in minutes)
# Default: 15 (reduced to 1 for testing)
gameserver.mixfight.portal.duration = 1

# Cron expression for MixFight event schedule
# Default: "0 0 14,20 * * ?" (2 PM and 8 PM daily)
# Format: "second minute hour day month dayOfWeek"
gameserver.mixfight.schedule = 0 0 14,20 * * ?

# Available map IDs for MixFight events (comma-separated)
# These should be PvP-enabled instance maps with MixFight instance handlers
# Default: 300030000,300040000,300250000 (Nochsana, Dark Poeta, Esoterrace)
gameserver.mixfight.maps = 300030000,300040000,300250000

# Portal NPC IDs for MixFight portals
# Default: 831073 (same for both factions)
gameserver.mixfight.portal.sanctum.npc = 831073
gameserver.mixfight.portal.pandaemonium.npc = 831073

# Portal spawn locations (x,y,z,heading)
# Sanctum portal location
gameserver.mixfight.portal.sanctum.location = 1321.9839,1513.3536,567.9099,0
# Pandaemonium portal location
gameserver.mixfight.portal.pandaemonium.location = 1664.8394,1399.1025,194.66542,0

# Custom name displayed for MixFight portal NPCs
# Default: MIXFIGHT PORTAL
gameserver.mixfight.portal.name = MIXFIGHT PORTAL

# Points multiplier for kills during MixFight events
# Default: 2.0
gameserver.mixfight.points.multiplier = 2.0

# Reward configuration (format: minAP:itemId:itemCount:kinah:honorPoints)
# Tier 1 rewards (top performers)
gameserver.mixfight.rewards.tier1 = 10000:186000242:10:500000:100
# Tier 2 rewards
gameserver.mixfight.rewards.tier2 = 5000:186000242:5:250000:80
# Tier 3 rewards
gameserver.mixfight.rewards.tier3 = 2000:186000242:4:100000:70
# Tier 4 rewards
gameserver.mixfight.rewards.tier4 = 1000:186000242:3:50000:60

# Participation rewards (format: itemId:itemCount:kinah:honorPoints)
gameserver.mixfight.rewards.participation = 186000242:1:25000:30

# Maximum number of players per MixFight instance
# Default: 50
gameserver.mixfight.max.players = 50

# Enable announcements for MixFight events
# Default: true
gameserver.mixfight.announcements = true

# Announcement intervals in minutes before event starts
# Default: 30,15,5,1
gameserver.mixfight.announcement.intervals = 30,15,5,1

# ----------------------------
# MixFight Spawn Coordinates:
# Format: x,y,z,heading separated by semicolons for multiple spawn points
# ----------------------------

# Nochsana Training Camp (300030000) spawn coordinates
gameserver.mixfight.spawn.300030000 = 516.2301,665.2475,330.98575,80;505.0609,556.5992,331.875,24;356.20883,649.88837,374.92065,94;361.09503,594.8203,343.4533,110;504.8307,552.8712,331.73163,28;396.7961,434.89804,367.375,49;319.3241,482.66284,362.1666,113;378.744772,407.0372,376.87595,33;361.61758,426.42648,378.05753,73;396.9945,284.7902,378.83838,30;285.6835,349.3712,380.09332,6;337.03836,356.899923,392.41467,30;331.6864,271.93954,384.55338,25;383.3997,504.6931,360.22946,20;430.06927,503.45038,359.9095,70

# Dark Poeta (300040000) spawn coordinates
gameserver.mixfight.spawn.300040000 = 1230.3514,409.39297,140.125,67;1052.1534,311.02072,132.80185,41;972.8223,530.3805,101.1753855,78;832.4362,577.91846,118.75,99;641.57635,354.30344,103.125,19;557.3073,490.47943,107.55174,2;517.4669,671.3205,115.58252,8;689.1145,861.0198,125.9925,62;581.9522,971.206,128.01974,79;433.6868,1056.1982,119.75,9;561.6822,1151.2819,139.39552,69;334.62918,1175.837,151.73123,31;234.94008,1302.3478,150.77277,114;375.23392,1316.1445,156.88617,80;361.7095,1247.0088,155.23184,97

# Esoterrace (300250000) spawn coordinates
gameserver.mixfight.spawn.300250000 = 844.81775,578.80804,180.85222,110;952.45197,424.6489,218.1969,7;1006.8982,514.4989,248.643955,27;967.2982,657.34344,256.875,38;1225.7998,483.6569,265.76254,82;1297.7977,609.86475,296.66135,20;1238.2762,577.2078,294.89227,104;1272.1031,774.35925,261.0,43;1224.0793,963.01166,326.98068,93;1131.2866,886.30255,316.6489,80;1116.6604,817.08105,316.79816,0;1076.9366,970.67316,322.39038,92;1251.3419,866.95953,320.96628,40;1285.4011,891.6742,318.875,39;1026.0908,794.0275,257.75,65

# ----------------------------
# OneVsOne PvP config:
# ----------------------------
# Enable the OneVsOne custom PvP system
# 1v1 PvP matches using portal-based queue system
# Default: true
gameserver.onevsone.enable = true

# Minimum level required to participate in OneVsOne matches
# Default: 30
gameserver.onevsone.min.level = 30

# Maximum level difference allowed between matched players
# Default: 10 (players within 10 levels can be matched)
gameserver.onevsone.max.level.diff = 10

# Queue timeout in minutes (how long players wait before being removed from queue)
# Default: 10
gameserver.onevsone.queue.timeout = 10

# Match duration in minutes
# Default: 15
gameserver.onevsone.match.duration = 15

# Available map IDs for OneVsOne matches (comma-separated)
# These should be small PvP-enabled instance maps
# Default: 300030000 (Nochsana Training Camp - proven working map)
gameserver.onevsone.maps = 300030000

# Portal NPC ID for OneVsOne (portal 207011)
# Default: 207011
gameserver.onevsone.portal.npc = 207011

# Portal spawn locations (world_id:x,y,z,heading separated by semicolon)
# Default: Sanctum and Pandaemonium same locations as MixFight
gameserver.onevsone.portal.locations = 110010000:1321.9839,1513.3536,567.9099,0;120010000:1664.8394,1399.1025,194.66542,0

# Custom name displayed for OneVsOne portal NPCs
# Default: OneVsOne Portal
gameserver.onevsone.portal.name = OneVsOne Portal

# Winner rewards (itemId:itemCount:kinah)
# Default: Ceramium Medal and kinah
gameserver.onevsone.rewards.winner = 186000242:2:100000

# Loser rewards (itemId:itemCount:kinah)
# Default: Participation reward
gameserver.onevsone.rewards.loser = 186000147:5:25000

# Enable announcements for OneVsOne matches
# Default: true
gameserver.onevsone.announcements = true

# Enable cross-faction matching (Elyos vs Asmodians)
# Default: true
gameserver.onevsone.cross.faction = true

# Auto-start OneVsOne system on server startup
# If false, requires admin command //1v1 start to activate
# Default: false
gameserver.onevsone.auto.start = true

# Schedule for automatic OneVsOne system activation (cron expression)
# Only used if auto.start is true
# Default: empty (no automatic scheduling)
# Example: "0 0 12,18 * * ?" (start at 12:00 and 18:00 daily)
gameserver.onevsone.schedule = 0 0 12,18 * * ?

# ----------------------------
# OneVsOne Spawn Coordinates:
# Format: x,y,z,heading separated by semicolons for multiple spawn points
# ----------------------------

# Nochsana Training Camp (300030000) spawn coordinates - using proven working coordinates
gameserver.onevsone.spawn.300030000 = 516.2301,665.2475,330.98575,80;505.0609,556.5992,331.875,24

# ----------------------------
# FFA (Free-For-All) Event config:
# ----------------------------
# Enable the FFA custom PvP event system
# All players teleport to a single map where everyone can attack each other
# Default: true
gameserver.ffa.enable = true

# Minimum level required to participate in FFA events
# Default: 50
gameserver.ffa.min.level = 50

# Duration of FFA event in minutes
# Default: 30
gameserver.ffa.duration = 30

# Maximum number of players per FFA event
# Default: 100
gameserver.ffa.max.players = 100

# Cron expression for FFA event schedule
# Default: "0 0 16,22 * * ?" (4 PM and 10 PM daily)
# Format: "second minute hour day month dayOfWeek"
gameserver.ffa.schedule = 0 0 16,22 * * ?

# Auto-start FFA system on server startup
# If false, requires admin command //ffa start to activate
# Default: false
gameserver.ffa.auto.start = false

# Enable announcements for FFA events
# Default: true
gameserver.ffa.announcements = true

# FFA event map ID (fallback if random selection fails)
# Default: 300040000 (Dark Poeta)
gameserver.ffa.map.id = 300040000

# Available map IDs for FFA events (comma-separated)
# These should be PvP-enabled instance maps with FFA instance handlers
# Default: 300030000,300040000,300250000 (Nochsana, Dark Poeta, Esoterrace)
gameserver.ffa.maps = 300030000,300040000,300250000

# FFA event spawn coordinates per map
# Format: x,y,z,heading separated by semicolons for multiple spawn points
# Each map has 15 spawn points for random player placement

# Nochsana Training Camp (300030000) spawn coordinates
gameserver.ffa.spawn.300030000 = 516.2301,665.2475,330.98575,80;505.0609,556.5992,331.875,24;356.20883,649.88837,374.92065,94;361.09503,594.8203,343.4533,110;504.8307,552.8712,331.73163,28;396.7961,434.89804,367.375,49;319.3241,482.66284,362.1666,113;378.74472,407.0372,376.87595,33;361.61758,426.42648,378.05753,73;396.9945,284.7902,378.83838,30;285.6835,349.3712,380.09332,6;337.03836,356.89923,392.41467,30;331.6864,271.93954,384.55338,25;383.3997,504.6931,360.22946,20;430.06927,503.45038,359.9095,70

# Dark Poeta (300040000) spawn coordinates
gameserver.ffa.spawn.300040000 = 1230.3514,409.39297,140.125,67;1052.1534,311.02072,132.80185,41;972.8223,530.3805,101.175385,78;832.4362,577.91846,118.75,99;641.57635,354.30344,103.125,19;557.3073,490.47943,107.55174,2;517.4669,671.3205,115.58252,8;689.1145,861.0198,125.9925,62;581.9522,971.206,128.01974,79;433.6868,1056.9828,119.75,9;561.6822,1151.2819,139.39552,69;334.62918,1175.837,151.73123,31;234.94008,1302.3478,150.77277,114;375.23392,1316.1445,156.88617,80;361.7095,1247.0088,155.23184,97

# Esoterrace (300250000) spawn coordinates
gameserver.ffa.spawn.300250000 = 844.81775,578.80804,180.85222,110;952.45197,424.6489,218.1969,7;1006.8982,514.4989,248.64395,27;967.2982,657.34344,256.875,38;1225.7998,483.6569,265.76254,82;1297.7977,609.86475,296.66135,20;1238.2762,577.2078,294.89227,104;1272.1031,774.35925,261.0,43;1224.0793,963.01166,326.98068,93;1131.2866,886.30255,316.6489,80;1116.6604,817.08105,316.79816,0;1076.9366,970.67316,322.39038,92;1251.3419,866.95953,320.96628,40;1285.4011,891.6742,318.875,39;1026.0908,794.0275,257.75,65

# Legacy fallback spawn coordinates (used if map-specific coordinates not found)
gameserver.ffa.spawn.coords = 242.52405,424.71637,103.80612,0

# 1st place rewards (itemId:itemCount:kinah)
# Default: Ceramium Medal and kinah
gameserver.ffa.rewards.first = 186000242:10:500000

# 2nd place rewards (itemId:itemCount:kinah)
# Default: Ceramium Medal and kinah
gameserver.ffa.rewards.second = 186000242:5:300000

# 3rd place rewards (itemId:itemCount:kinah)
# Default: Ceramium Medal and kinah
gameserver.ffa.rewards.third = 186000242:5:200000

# Participation rewards for all other players (itemId:itemCount:kinah)
# Default: Ceramium Medal and kinah
gameserver.ffa.rewards.participation = 186000147:3:50000

# General FFA rewards summary (for display purposes)
# Default: Summary of all reward tiers
gameserver.ffa.rewards = 1st: 186000242:10:500000, 2nd: 186000242:5:300000, 3rd: 186000242:5:200000, Participation: 186000147:3:50000

# FFA teleportation coordinates when event ends
# Elyos capital city teleport (mapId,x,y,z,heading)
# Default: Sanctum coordinates
gameserver.ffa.teleport.elyos = 110010000,1313.25,1512.011,568.107,0

# Asmodians capital city teleport (mapId,x,y,z,heading)
# Default: Pandaemonium coordinates
gameserver.ffa.teleport.asmodians = 120010000,1685.7,1400.5,195.48618,60

# ----------------------------
# Rifts config's:
# ----------------------------
gameserver.rift.enable = true
gameserver.rift.duration = 1

# ----------------------------
# Dimensional Vortex config's:
# ----------------------------
gameserver.vortex.enable = true
gameserver.vortex.brusthonin.schedule = 0 0 16 ? * SAT
gameserver.vortex.theobomos.schedule = 0 0 16 ? * SUN
gameserver.vortex.duration = 2

# ----------------------------
# Conqueror & Protector system:
# ----------------------------
gameserver.cp.enable = true
gameserver.cp.worlds = 210020000,210040000,210050000,210070000,220020000,220040000,220070000,220080000
gameserver.cp.level.diff = 5
gameserver.cp.kills.decrease_interval_minutes = 10
gameserver.cp.kills.decrease_count = 1
gameserver.cp.kills.rank1 = 1
gameserver.cp.kills.rank2 = 10
gameserver.cp.kills.rank3 = 20

# ----------------------------
# Limits config:
# ----------------------------
# Enable limits 
# Default: true
gameserver.limits.enable = true

# Enable dynamic cap: When active, you can sell one item that would exceed your limit, before you finally reach it
# Default: false
gameserver.limits.enable_dynamic_cap = false

# Limit update time
# Default: 00:00 everyday
gameserver.limits.update = 0 0 0 ? * *

# ----------------------------
# Cap config's:
# ----------------------------
# Enable Kinah capping
# Default: false
gameserver.enable.kinah.cap = false

# Cap value for Kinah
# Default: 999999999
gameserver.kinah.cap.value = 999999999

# Enable AP capping
# Default: false
gameserver.enable.ap.cap = false

# Cap value for AP
# Default: 1000000
gameserver.ap.cap.value = 1000000

# ----------------------------
# Friendlist config's:
# ----------------------------
# Friendlist size (max 90)
# Default: 90
gameserver.friendlist.size = 90

# Restrict GMs so they can not have non-GMs on their list
# Default: false
gameserver.friendlist.gm_restrict = false

# ----------------------------
# Size config's:
# ----------------------------
# Allow to configure size limit of basic quests that player can get up
# Default: 40
gameserver.basic.questsize.limit = 40

# Total number of allowed cube expansions
# Default: 11
gameserver.cube.expansion_limit = 11

# Allow to configure cube size limit of npc expands
# Default: 5
gameserver.npcexpands.limit = 5

# ----------------------------
# Search config's:
# ----------------------------
# Enable searching players of opposite faction
# Default: false
gameserver.search.factions.mode = false

# Enable list gm when searching players
# Default: false
gameserver.search.gm.list = false

# Minimum level to use search
# Default: 10
gameserver.search.player.level = 10

# ----------------------------
# Abyss Xform after logout
# Enable or Disable counting down of duration after logout of Abyss transformations
# ----------------------------
gameserver.abyssxform.afterlogout = false

# ----------------------------
# .faction Command
# ----------------------------
# Kinah price
gameserver.faction.price = 10000
# Enable the command
gameserver.faction.cmdchannel = true
# Use chat server channels
gameserver.faction.chatchannels = false

# Enable selling ap items
# Default: true
gameserver.selling.apitems.enabled = true

# Time in minutes after which character will be deleted.
# Default: 5
character.deletion.time.minutes = 5

# ----------------------------
# Starter Kit:
# ----------------------------
# Enable starter kit
# Default: false
gameserver.custom.starter_kit.enable = true

# Enables joining the custom PvP map via .pvp chat command
# Default: false
gameserver.pvpmap.enable = true

# PvP AP multiplier for our custom PvP-Map.
# Keep in mind that this value is also multiplied with players rates!
# Default: 2 (equals x4 for our current rates)
gameserver.pvpmap.apmultiplier = 2

# PvE AP multiplier for our custom PvP-Map.
# Keep in mind that this value is also multiplied with players rates!
# Default: 1
gameserver.pvpmap.pve.apmultiplier = 1

# Random boss spawn rate for our custom PvP-Map
# Keep in mind that this value is increased by the
# amount of online players(+2% per player, cap 30%)
# Default: 40 (+30% max online player bonus = 70)
gameserver.pvpmap.random_boss.rate = 40

# Random boss spawn time for our custom PvP-Map
# Default: 0 30 14,18,21 ? * *
gameserver.pvpmap.random_boss.time = 0 30 14,18,21 ? * *

# ----------------------------
# Godstones:
# ----------------------------
# Default: 1.0
gameserver.rates.godstone.activation.rate = 1.0

# Wait time in milliseconds between evaluations of a Godstone activation.
# Default: 750
gameserver.rates.godstone.evaluation.cooldown_millis = 750